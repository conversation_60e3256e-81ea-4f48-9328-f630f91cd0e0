<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel: A Modern PHP Framework</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            background: linear-gradient(135deg, #ff2d20 0%, #ff6b35 25%, #f7931e 50%, #ffcc02 75%, #ff2d20 100%);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .presentation-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 95%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px 60px;
            box-shadow: 
                0 25px 50px rgba(255, 45, 32, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid rgba(255, 45, 32, 0.1);
            position: relative;
            overflow-y: auto;
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff2d20, #f7931e, #ff2d20);
            border-radius: 25px 25px 0 0;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        h1 {
            font-size: 4.5em;
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 800;
            letter-spacing: -2px;
            text-shadow: 0 2px 4px rgba(255, 45, 32, 0.1);
        }

        h2 {
            font-size: 3em;
            color: #2d3748;
            margin-bottom: 40px;
            text-align: center;
            font-weight: 700;
            position: relative;
            letter-spacing: -1px;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #ff2d20, #f7931e);
            border-radius: 2px;
        }

        h3 {
            font-size: 1.8em;
            color: #2d3748;
            margin-bottom: 25px;
            font-weight: 600;
        }

        .subtitle {
            font-size: 1.8em;
            color: #4a5568;
            text-align: center;
            margin-bottom: 25px;
            font-weight: 400;
        }

        .date {
            font-size: 1.4em;
            color: #718096;
            text-align: center;
            font-weight: 300;
        }

        ul {
            list-style: none;
            padding-left: 0;
        }

        li {
            font-size: 1.4em;
            margin-bottom: 18px;
            padding: 15px 20px;
            position: relative;
            color: #2d3748;
            line-height: 1.6;
            background: rgba(255, 45, 32, 0.03);
            border-radius: 12px;
            border-left: 4px solid #ff2d20;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        li:hover {
            background: rgba(255, 45, 32, 0.08);
            transform: translateX(5px);
        }

        li:before {
            content: "▶";
            color: #ff2d20;
            position: absolute;
            left: -5px;
            font-size: 0.8em;
        }

        .feature-item {
            background: linear-gradient(135deg, rgba(255, 45, 32, 0.05), rgba(247, 147, 30, 0.05));
            padding: 25px;
            margin: 20px 0;
            border-radius: 15px;
            border: 1px solid rgba(255, 45, 32, 0.1);
            box-shadow: 0 4px 15px rgba(255, 45, 32, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #ff2d20, #f7931e);
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 45, 32, 0.15);
        }

        .feature-title {
            font-weight: 700;
            color: #ff2d20;
            font-size: 1.3em;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-description {
            color: #4a5568;
            font-size: 1.1em;
            font-weight: 400;
        }

        .code-block {
            background: linear-gradient(135deg, #1a202c, #2d3748);
            color: #e2e8f0;
            padding: 35px;
            border-radius: 15px;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 1.2em;
            line-height: 1.8;
            margin: 25px 0;
            overflow-x: auto;
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 45, 32, 0.2);
            position: relative;
        }

        .code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff2d20, #f7931e, #ff2d20);
        }

        .php-keyword { color: #66d9ef; font-weight: 600; }
        .php-class { color: #a6e22e; font-weight: 600; }
        .php-string { color: #e6db74; }
        .php-comment { color: #75715e; font-style: italic; }

        .mvc-diagram {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 50px 0;
        }

        .mvc-box {
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            color: white;
            padding: 40px 25px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(255, 45, 32, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .mvc-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
            pointer-events: none;
        }

        .mvc-box:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 45px rgba(255, 45, 32, 0.4);
        }

        .mvc-box h4 {
            font-size: 1.8em;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .mvc-box p {
            font-size: 1.1em;
            line-height: 1.5;
            opacity: 0.95;
        }

        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .pros {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(72, 187, 120, 0.05));
            padding: 35px;
            border-radius: 20px;
            border: 2px solid rgba(72, 187, 120, 0.2);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.1);
        }

        .cons {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(245, 101, 101, 0.05));
            padding: 35px;
            border-radius: 20px;
            border: 2px solid rgba(245, 101, 101, 0.2);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.1);
        }

        .pros h3 {
            color: #38a169;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .cons h3 {
            color: #e53e3e;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .thank-you {
            text-align: center;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .thank-you h1 {
            font-size: 6em;
            margin-bottom: 40px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .laravel-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3.5em;
            font-weight: 800;
            margin: 0 auto 40px;
            box-shadow: 
                0 20px 40px rgba(255, 45, 32, 0.3),
                0 0 0 8px rgba(255, 45, 32, 0.1);
            position: relative;
            animation: logoFloat 3s ease-in-out infinite;
        }

        .laravel-logo::before {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            right: -15px;
            bottom: -15px;
            border: 2px solid rgba(255, 45, 32, 0.2);
            border-radius: 50%;
            animation: logoRing 4s linear infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        @keyframes logoRing {
            0% { transform: rotate(0deg) scale(1); opacity: 0.3; }
            50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.3; }
        }

        .companies {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 50px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .company-badge {
            background: linear-gradient(135deg, #2d3748, #1a202c);
            color: white;
            padding: 20px 35px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 1.2em;
            box-shadow: 0 10px 25px rgba(45, 55, 72, 0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 45, 32, 0.2);
        }

        .company-badge:hover {
            transform: translateY(-5px);
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            box-shadow: 0 15px 35px rgba(255, 45, 32, 0.4);
        }

        .navigation {
            position: fixed;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 45, 32, 0.2);
            padding: 18px 30px;
            border-radius: 35px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: 600;
            color: #2d3748;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 45, 32, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.5);
        }

        .nav-btn:disabled:hover {
            background: rgba(255, 255, 255, 0.5);
            transform: none;
            color: #2d3748;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .slide-counter {
            position: fixed;
            top: 40px;
            right: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 700;
            color: #2d3748;
            border: 2px solid rgba(255, 45, 32, 0.2);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slide-counter:hover {
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            color: white;
            transform: scale(1.1);
        }

        .slide-indicator {
            position: fixed;
            left: 40px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 45, 32, 0.3);
        }

        .indicator-dot.active {
            background: linear-gradient(135deg, #ff2d20, #f7931e);
            transform: scale(1.3);
            box-shadow: 0 0 15px rgba(255, 45, 32, 0.5);
        }

        .slide-title-icon {
            font-size: 0.8em;
            margin-right: 15px;
            opacity: 0.7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .slide {
                width: 98%;
                height: 90vh;
                padding: 30px 40px;
            }
            
            h1 { font-size: 3em; }
            h2 { font-size: 2.2em; }
            .mvc-diagram { grid-template-columns: 1fr; gap: 20px; }
            .pros-cons { grid-template-columns: 1fr; gap: 25px; }
            .companies { flex-direction: column; }
            
            .navigation {
                bottom: 20px;
                gap: 15px;
            }
            
            .nav-btn {
                padding: 12px 20px;
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active">
            <div class="laravel-logo">L</div>
            <h1>Laravel</h1>
            <div class="subtitle">A Modern PHP Framework</div>
            <div class="subtitle">Presented by Muhammad Ahmed</div>
            <div class="date">August 18, 2025</div>
        </div>

        <!-- Slide 2: Introduction to Laravel -->
        <div class="slide">
            <h2><i class="fas fa-rocket slide-title-icon"></i>Introduction to Laravel</h2>
            <div class="laravel-logo" style="width: 80px; height: 80px; font-size: 2em;">L</div>
            <ul>
                <li>Open-source PHP web framework</li>
                <li>Created by Taylor Otwell in 2011</li>
                <li>Designed for developer productivity and elegant syntax</li>
                <li>Follows Model-View-Controller (MVC) architecture</li>
                <li>Used for building robust, scalable web applications</li>
            </ul>
        </div>

        <!-- Slide 3: Why Choose Laravel? -->
        <div class="slide">
            <h2><i class="fas fa-star slide-title-icon"></i>Why Choose Laravel?</h2>
            <div class="feature-item">
                <div class="feature-title">
                    <i class="fas fa-database"></i>
                    Eloquent ORM
                </div>
                <div class="feature-description">Intuitive database interaction</div>
            </div>
            <div class="feature-item">
                <div class="feature-title">
                    <i class="fas fa-bolt"></i>
                    Blade Templating
                </div>
                <div class="feature-description">Lightweight, dynamic view engine</div>
            </div>
            <div class="feature-item">
                <div class="feature-title">
                    <i class="fas fa-terminal"></i>
                    Laravel Artisan
                </div>
                <div class="feature-description">Command-line tool for automation</div>
            </div>
            <div class="feature-item">
                <div class="feature-title">
                    <i class="fas fa-globe"></i>
                    Rich Ecosystem
                </div>
                <div class="feature-description">Includes Laravel Forge, Vapor, and Nova</div>
            </div>
            <ul>
                <li>Strong community and extensive documentation</li>
            </ul>
        </div>

        <!-- Slide 4: Key Features -->
        <div class="slide">
            <h2><i class="fas fa-cogs slide-title-icon"></i>Key Features of Laravel</h2>
            <ul>
                <li><strong>Routing:</strong> Flexible and expressive routing system</li>
                <li><strong>Middleware:</strong> Filters for authentication, logging, etc.</li>
                <li><strong>Eloquent Relationships:</strong> Simplifies one-to-many, many-to-many relations</li>
                <li><strong>Query Builder:</strong> Fluent interface for database queries</li>
                <li><strong>Built-in Security:</strong> CSRF protection, encryption, hashing</li>
            </ul>
        </div>

        <!-- Slide 5: MVC Architecture -->
        <div class="slide">
            <h2><i class="fas fa-sitemap slide-title-icon"></i>Laravel's MVC Architecture</h2>
            <div class="mvc-diagram">
                <div class="mvc-box">
                    <h4><i class="fas fa-database"></i> Model</h4>
                    <p>Manages data and business logic (e.g., database tables)</p>
                </div>
                <div class="mvc-box">
                    <h4><i class="fas fa-eye"></i> View</h4>
                    <p>Handles user interface and presentation</p>
                </div>
                <div class="mvc-box">
                    <h4><i class="fas fa-gamepad"></i> Controller</h4>
                    <p>Connects Model and View, processes user input</p>
                </div>
            </div>
            <ul>
                <li>Promotes separation of concerns for clean code</li>
            </ul>
        </div>

        <!-- Slide 6: Sample Code - Model -->
        <div class="slide">
            <h2><i class="fas fa-code slide-title-icon"></i>Sample Code: Creating a Model</h2>
            <div class="code-block">
<span class="php-comment">// Creating a Laravel Model</span>
<span class="php-keyword">&lt;?php</span>

<span class="php-keyword">namespace</span> <span class="php-class">App\Models</span>;

<span class="php-keyword">use</span> <span class="php-class">Illuminate\Database\Eloquent\Model</span>;

<span class="php-keyword">class</span> <span class="php-class">Post</span> <span class="php-keyword">extends</span> <span class="php-class">Model</span>
{
    <span class="php-keyword">protected</span> $fillable = [<span class="php-string">'title'</span>, <span class="php-string">'content'</span>];
    
    <span class="php-comment">// Define relationships, scopes, etc.</span>
}
            </div>
            <ul>
                <li>Defines a Post model for the posts table</li>
                <li>Supports mass assignment for easy CRUD operations</li>
                <li>Eloquent ORM simplifies database tasks</li>
            </ul>
        </div>

        <!-- Slide 7: Routing Example -->
        <div class="slide">
            <h2><i class="fas fa-route slide-title-icon"></i>Routing Example</h2>
            <div class="code-block">
<span class="php-comment">// routes/web.php</span>
<span class="php-keyword">use</span> <span class="php-class">App\Http\Controllers\PostController</span>;

<span class="php-comment">// RESTful routes</span>
<span class="php-class">Route</span>::<span class="php-keyword">get</span>(<span class="php-string">'/posts'</span>, [<span class="php-class">PostController</span>::<span class="php-keyword">class</span>, <span class="php-string">'index'</span>]);
<span class="php-class">Route</span>::<span class="php-keyword">post</span>(<span class="php-string">'/posts'</span>, [<span class="php-class">PostController</span>::<span class="php-keyword">class</span>, <span class="php-string">'store'</span>]);
<span class="php-class">Route</span>::<span class="php-keyword">get</span>(<span class="php-string">'/posts/{id}'</span>, [<span class="php-class">PostController</span>::<span class="php-keyword">class</span>, <span class="php-string">'show'</span>]);

<span class="php-comment">// Route groups and middleware</span>
<span class="php-class">Route</span>::<span class="php-keyword">middleware</span>(<span class="php-string">'auth'</span>)-><span class="php-keyword">group</span>(<span class="php-keyword">function</span> () {
    <span class="php-class">Route</span>::<span class="php-keyword">resource</span>(<span class="php-string">'posts'</span>, <span class="php-class">PostController</span>::<span class="php-keyword">class</span>);
});
            </div>
            <ul>
                <li>Maps HTTP requests to controller methods</li>
                <li>Supports RESTful routing conventions</li>
            </ul>
        </div>

        <!-- Slide 8: Laravel in Industry -->
        <div class="slide">
            <h2><i class="fas fa-industry slide-title-icon"></i>Laravel in Industry</h2>
            <div class="companies">
                <div class="company-badge"><i class="fas fa-tv"></i> BBC</div>
                <div class="company-badge"><i class="fas fa-pills"></i> Pfizer</div>
                <div class="company-badge"><i class="fas fa-laugh"></i> 9GAG</div>
                <div class="company-badge"><i class="fas fa-shopping-cart"></i> Laravel Nova</div>
            </div>
            <ul>
                <li>Ideal for e-commerce, CMS, and API-driven apps</li>
                <li>Supports microservices and monolithic architectures</li>
                <li>Integrates with Vue.js, React, and other front-end frameworks</li>
                <li>Scalable for startups to enterprise projects</li>
                <li>Powers millions of websites worldwide</li>
            </ul>
        </div>

        <!-- Slide 9: Advantages and Limitations -->
        <div class="slide">
            <h2><i class="fas fa-balance-scale slide-title-icon"></i>Advantages and Limitations</h2>
            <div class="pros-cons">
                <div class="pros">
                    <h3><i class="fas fa-check-circle"></i> Advantages</h3>
                    <ul>
                        <li>Rapid development with built-in tools</li>
                        <li>Strong security and scalability</li>
                        <li>Active community and frequent updates</li>
                        <li>Excellent documentation and learning resources</li>
                        <li>Rich package ecosystem (Packagist)</li>
                    </ul>
                </div>
                <div class="cons">
                    <h3><i class="fas fa-exclamation-triangle"></i> Limitations</h3>
                    <ul>
                        <li>Steeper learning curve for beginners</li>
                        <li>Performance overhead for very small projects</li>
                        <li>Can be overkill for simple websites</li>
                        <li>Requires good understanding of PHP</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 10: Thank You -->
        <div class="slide">
            <div class="thank-you">
                <div class="laravel-logo" style="animation: pulse 2s infinite;">L</div>
                <h1>Thank You!</h1>
                <div class="subtitle">Ready to build amazing things with Laravel?</div>
                <div class="subtitle" style="font-size: 1.4em; margin-top: 30px;">
                    <i class="fas fa-question-circle"></i> Questions?
                </div>
            </div>
        </div>
    </div>

    <div class="slide-counter" onclick="goToSlide(1)">
        <i class="fas fa-home"></i>
        <span id="current-slide">1</span> / <span id="total-slides">10</span>
    </div>

    <div class="navigation">
        <button onclick="previousSlide()" class="nav-btn">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button onclick="nextSlide()" class="nav-btn">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <div class="slide-indicator">
        <i class="fas fa-presentation"></i>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 10;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');

            if (n > totalSlides) currentSlide = 1;
            if (n < 1) currentSlide = totalSlides;

            slides.forEach(slide => slide.classList.remove('active'));
            slides[currentSlide - 1].classList.add('active');

            document.getElementById('current-slide').textContent = currentSlide;
            document.getElementById('total-slides').textContent = totalSlides;
        }

        function nextSlide() {
            currentSlide++;
            showSlide(currentSlide);
        }

        function previousSlide() {
            currentSlide--;
            showSlide(currentSlide);
        }

        function goToSlide(n) {
            currentSlide = n;
            showSlide(currentSlide);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                nextSlide();
            } else if (event.key === 'ArrowLeft') {
                previousSlide();
            } else if (event.key === 'Home') {
                goToSlide(1);
            } else if (event.key === 'End') {
                goToSlide(totalSlides);
            }
        });

        // Initialize
        showSlide(currentSlide);
    </script>

</body>
</html>